import React, { useEffect, useState } from 'react';

import axios from 'axios';
import { FormControl, InputLabel, Select, MenuItem } from '@mui/material';
import type { SelectChangeEvent } from '@mui/material';

interface FinancialYearFilterProps {
  filters: Record<string, any>;
  setFilters: (filters: Record<string, any> | ((prevFilters: Record<string, any>) => Record<string, any>)) => void;
  api: string;
  hideMonthSelect?: boolean;
}

interface FinancialYear {
  id: string;
  year_financial: string;
  fy_start_date: string;
  fy_end_date: string;
  closed?: boolean;
}

interface Month {
  label: string;
  value: string;
}

export default function FinancialYearFilterMUI({ filters, setFilters, api, hideMonthSelect = false }: FinancialYearFilterProps) {
  const [financialYears, setFinancialYears] = useState<FinancialYear[]>([]);
  const [months, setMonths] = useState<Month[]>([]);                  // will hold an array of { label, value } for months

  // Fetch financial years on component mount
  useEffect(() => {
    const fetchFinancialYears = async () => {
      try {
        const response = await axios.get(
          api,
          {
            baseURL: process.env.NEXT_PUBLIC_API_URL,
            withCredentials: true,
          }
        );


        // The data is in response?.data?.data
        if (response?.data?.data) {
          setFinancialYears(response.data.data);
        }
      } catch (error) {
        console.warn('Error fetching financial years:', error);
      }
    };

    fetchFinancialYears();
  }, [api]);

  // When user changes the Year
  const handleYearChange = (event: SelectChangeEvent) => {
    const newSelectedYearId = event.target.value as string;

    setFilters((prevFilters: Record<string, any>) => ({
      ...prevFilters,
      financial_year: newSelectedYearId,
      financial_month: '', // reset month to "All Months"
    }));

    if (!newSelectedYearId) {
      // user cleared the year selection
      setMonths([]);

      return;
    }

    // Find the matching FY object by its id
    const fyObj = financialYears.find((item) => item.id === newSelectedYearId);

    // If we have valid start/end
    if (fyObj?.fy_start_date && fyObj?.fy_end_date) {
      generateMonths(fyObj.fy_start_date, fyObj.fy_end_date);
    } else {
      setMonths([]);
    }
  };

  // When user changes the Month
  const handleMonthChange = (event: SelectChangeEvent) => {
    setFilters((prevFilters: Record<string, any>) => ({
      ...prevFilters,
      financial_month: event.target.value,
    }));
  };

  // Build an array of { label, value } for each month between start & end
  // For example:
  //   label = "June 2019"
  //   value = "6-2019"
  const generateMonths = (start: string, end: string) => {
    const startDate = new Date(start);
    const endDate = new Date(end);

    // We won't include "All Months" in this array;
    // we handle that as a separate default or your choice
    const generatedMonths = [];

    const current = new Date(startDate);

    while (current <= endDate) {
      const year = current.getFullYear();

      // Months in JS are 0-based, so +1 to get normal month
      const monthIndex = current.getMonth() + 1;

      const label = current.toLocaleString('default', {
        month: 'long',
        year: 'numeric',
      });

      const value = `${monthIndex}-${year}`; // e.g. "6-2019"

      generatedMonths.push({ label, value });

      // Move to next month
      current.setMonth(current.getMonth() + 1);
    }

    setMonths(generatedMonths);
  };

  return (
    <>
      {/* Financial Year Select */}
      <FormControl variant="outlined" sx={{ minWidth: 200 }}>
        <InputLabel id="financial-year-label" shrink>
          Financial Year
        </InputLabel>
        <Select
          labelId="financial-year-label"
          label="Financial Year"

          // If you're also controlling from parent, you might do something like:
          // value={filters?.financial_year ?? selectedYearId}
          value={filters?.financial_year ?? ''}
          onChange={handleYearChange}
          size="small"
          displayEmpty
        >
          <MenuItem value="">
            View all transactions for
          </MenuItem>

          {/* Use fy.id as the <option> value, but show fy.year_financial as text */}
          {financialYears.map((fy) => (
            <MenuItem key={fy.id} value={fy.id}>
              {fy.year_financial}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Month Select - Only show if hideMonthSelect is false */}
      {!hideMonthSelect && (
        <FormControl
          variant="outlined"
          sx={{ minWidth: 200 }}
          disabled={!filters?.financial_year}
        >
          <InputLabel id="month-label" shrink>Month</InputLabel>
          <Select
            labelId="month-label"
            label="Month"

            // value={filters?.financial_month ?? selectedMonth}
            value={filters?.financial_month ?? ''}
            onChange={handleMonthChange}
            size="small"
            displayEmpty
          >
            {/* "All Months" item -> can remain a string or could store "all" */}
            <MenuItem value="">All Months</MenuItem>

            {/* For each { label, value } in months */}
            {months.map((monthObj, index) => (
              <MenuItem key={index} value={monthObj.value}>
                {monthObj.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      )}
    </>
  );
}
