import { useQuery } from '@tanstack/react-query';
import type {
  MRT_SortingState,
  MRT_ColumnFiltersState,
  MRT_PaginationState,
} from 'material-react-table';

import type {
  TableSchema,
  ExtraFilter,
  ApiResponse,
} from '../types/tableTypes';
import { GenerateColumnsForMRT } from '../components/GenerateColumns';
import tableInterceptor from '../components/tableInterceptor';
import { toast } from 'react-toastify';

const getExtraFilters = (table: any): ExtraFilter[] => {
  const xf = table?.extraFilters || [];

  if (Array.isArray(xf)) return xf;

  return Object.entries(xf)
    .map(([k, v]) => {
      if (typeof v === 'object') return { key: k, ...v };
      if (typeof v === 'string') return { key: k, title: v };

      return null;
    })
    .filter(Boolean) as ExtraFilter[];
};

const getNumberOfTables = (columns: any[]): number => {
  if (!Array.isArray(columns)) return 0;
  if (columns.length === 0) return 0;

  return Array.isArray(columns[0]) ? columns.length : 1;
};

interface UseTableSchemaParams {
  customURL?: string | null;
  isGate?: boolean;
  currentTab: string | null;
  asPath: string | null;
  globalFilter: string;
  columnFilters: MRT_ColumnFiltersState;
  sorting: MRT_SortingState;
  extraFilters: Record<string, any>;
  pagination: MRT_PaginationState;
  rows?: ApiResponse['data']['data'];
  schema?: ApiResponse['data']['meta'];
  handleSaveCell?: (cellID: string | number, columnID: string | number, value: any) => void | null;
  queryKey?: any[];
  shouldAllowBack?: boolean;
}

const useTableSchema = ({
  customURL,
  currentTab,
  asPath,
  globalFilter,
  columnFilters,
  sorting,
  extraFilters,
  pagination,
  schema,
  rows,
  handleSaveCell = null,
  queryKey = [],
  shouldAllowBack = false,
}: UseTableSchemaParams) => {
  const fetchSchema = async (): Promise<TableSchema> => {
    try {
      const sortObj = sorting.reduce<Record<string, 'asc' | 'desc'>>((acc, { id, desc }) => {
        acc[id] = desc ? 'desc' : 'asc';

        return acc;
      }, {});

      const filterObj = columnFilters.reduce<Record<string, any>>((acc, { id, value }) => {
        acc[id] = value;

        return acc;
      }, {});

      let result =
        schema
          ? { data: { data: rows, meta: schema }, status: 200, message: 'OK' }
          : null;

      if (!result) {
        result = await tableInterceptor(customURL || asPath || '', {
          params: {
            filters: {
              ...(globalFilter ? { search: globalFilter } : {}),
              ...filterObj,
              ...extraFilters,
            },
            ...(currentTab && { current_tab: currentTab }),
            sort: sortObj,
            page: pagination.pageIndex + 1,
            per_page: pagination.pageSize,
          },
          timeout: 30000,
          validateStatus: () => true,
        });
      }

      const rawData = result?.data?.data as any;
      const dataArray = Array.isArray(rawData[0]) ? rawData : [rawData];
      const tableMeta: any = result?.data?.meta?.schema?.table || {};

      if (!result || (result.status >= 400 && !tableMeta?.columns)) {
        throw new Error(result?.data?.message || result?.message || 'Network error');
      }

      const isEditable =
        tableMeta.columns?.some((c: any) => c.editable) || tableMeta.orderable;

      const generatedColumns = GenerateColumnsForMRT(tableMeta, () => query.refetch(), handleSaveCell);

      // Only use helpNote if it exists in the schema
      const helpNote = tableMeta.helpNote && Array.isArray(tableMeta.helpNote) ? tableMeta.helpNote : undefined;
      const initialActions = tableMeta.actions || [];

      return {
        rows: dataArray,
        editedRows:
          isEditable && tableMeta.edit_mode !== 'row' ? dataArray : [],
        columns: generatedColumns?.map((table) => table?.filter((col: any) => col?.id)) || [],
        totalTables: getNumberOfTables(dataArray),
        actions: shouldAllowBack
          ? initialActions
          : initialActions.filter(action => action.go_back !== true),
        fields: tableMeta.fields || [],
        tableTitle: tableMeta.tableTitle || '',
        note: tableMeta.note || '',
        total: result.data.meta.pagination?.total || 0,
        tabs: tableMeta.tabs || [],
        options: tableMeta.options || [],
        filters: tableMeta.filters || {},
        extraFilters: getExtraFilters(tableMeta),
        selectBy: tableMeta.select_by || null,
        multiSelect: tableMeta.multi_select || false,
        filterBy: tableMeta.filter_by || {},
        selectActions: tableMeta.select_actions || [],
        isSearchable: tableMeta.is_searchable || false,
        enableSelectBy: !!Object.keys(tableMeta.select_by || {}).length,
        enableFilterBy: !!Object.keys(tableMeta.filter_by || {}).length,
        editMode: tableMeta.edit_mode || 'table',
        isEditable,
        bulkUpdatePath: tableMeta.bulk_update_path || null,
        updatePath: tableMeta.update_path || null,
        isAddable: tableMeta.add_row || false,
        rowActions: generatedColumns?.map((table) => table?.findLast((col: any) => !!col?.actions)?.actions) || [],
        orderable: tableMeta.orderable || false,
        sendFullParams: tableMeta.send_full_params || false,
        enableSelectOn: tableMeta.enable_select_on || null,
        tableDirection: tableMeta.tableDirection || 'column',
        meta: result.data.meta || {},
        helpNote,
        hideTableIfEmpty: tableMeta.hide_table || false,
      };
    }
    catch (error) {
      console.warn('Error fetching table schema:', error);
      toast.error(error?.message || 'Failed to load table schema. Please try again later.');
      
      // throw error;
    }
  };

  const query = useQuery({
    queryKey,
    queryFn: fetchSchema,
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: true,
    enabled:
      !!schema ||
      (!!asPath && !['/form/', 'admin/select', '/help/'].some((f) => asPath.includes(f))),
    placeholderData: (prev) => prev,
  });

  return query;
};

export default useTableSchema;
