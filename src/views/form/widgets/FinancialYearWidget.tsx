import {
  Typography,
  Table,
  TableBody,
  TableRow,
  TableCell,
  Paper,
  TextField,
} from "@mui/material";
import type { RJSFSchema, WidgetProps } from "@rjsf/utils";

// interface FinancialYearData {
//   id: string | number;
//   fy_start_date: string;
//   fy_end_date: string;
//   closed: boolean;
//   opening_balance?: string | number | null;
// }

const FinancialYearWidget = ({
  value,
  onChange,
  options,
}: WidgetProps<any, RJSFSchema, any>) => {
  const showOpeningBalance = options.showOpeningBalance !== false;
  const highlightCurrent = options.highlightCurrent !== false;

  return (
    <Paper variant="outlined" sx={{ overflow: "hidden" }}>
      <Table size="small">
        <TableBody>
          {value?.map((year, index) => (
            <TableRow
              key={year?.fy_start_date || index}
              sx={(theme) => ({
                ...(highlightCurrent && !year.closed && {
                  backgroundColor: theme.palette.action.hover
                }),
                "&:hover": {
                  backgroundColor: theme.palette.action.hover
                }
              })}
            >
              {showOpeningBalance && (
                <TableCell
                  sx={{
                    fontWeight: "bold",
                    borderBottom: 0,
                    width: "30%",
                    verticalAlign: "top",
                    pt: 2
                  }}
                >
                  Opening Balance
                </TableCell>
              )}
              <TableCell
                sx={{
                  borderBottom: 0,
                  width: "30%",
                  verticalAlign: "top",
                  pt: 2
                }}
              >
                  <TextField
                    type={year?.is_editable ? "number" : "text"}
                    fullWidth
                    variant="standard"
                    size="small"
                    disabled={!year?.is_editable}
                    placeholder={year?.opening_balance ?? "0"}
                    value={year?.opening_balance || ""}
                    onChange={(e) => {
                      const updatedYear = {
                        ...year,
                        opening_balance: e.target.value
                      };

                      onChange(
                        value.map((y) =>
                          y?.fy_start_date === year?.fy_start_date ? updatedYear : y
                        )
                      );
                    }}
                  />
              </TableCell>
              <TableCell
                sx={{
                  borderBottom: 0,
                  width: "40%",
                  verticalAlign: "top",
                  pt: 2
                }}
              >
                <Typography
                  sx={{
                    color: year.closed ? "text.secondary" : "text.primary",
                    fontWeight: year.closed ? "normal" : "bold",
                  }}
                >
                  Financial Year :
                </Typography>
                <Typography
                  sx={{
                    color: "success.main",
                    fontWeight: "medium",
                  }}
                >
                  <span>
                    {new Date(year.fy_start_date).toLocaleDateString()} -{" "}
                    {new Date(year.fy_end_date).toLocaleDateString()}
                  </span>
                </Typography>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Paper>
  );
};

export default FinancialYearWidget;
