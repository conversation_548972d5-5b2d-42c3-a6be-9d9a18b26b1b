{"meta": {"title": "Create Group"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "lstParentAccount", "path": "id", "operator": "falsy"}]}, "event": [{"type": "uiOverride", "params": {"nature": {"ui:disabled": true, "ui:help": "Please select a group first"}}}]}, {"conditions": {"any": [{"fact": "lstParentAccount", "path": "nature_of_account", "operator": "equal", "value": "dr"}]}, "event": [{"type": "update", "params": {"nature": "Debit"}}]}, {"conditions": {"any": [{"fact": "lstParentAccount", "path": "nature_of_account", "operator": "equal", "value": "cr"}]}, "event": [{"type": "update", "params": {"nature": "Credit"}}]}, {"conditions": {"all": [{"fact": "lstParentAccount", "path": "behaviour", "operator": "equal", "value": "expense"}]}, "event": [{"type": "uiOverride", "params": {"income_type": {"ui:title": "Expense Type"}}}]}, {"conditions": {"any": [{"fact": "lstParentAccount", "path": "behaviour", "operator": "isNotInArray", "value": ["expense", "income"]}]}, "event": [{"type": "remove", "params": {"field": ["income_type"]}}]}, {"conditions": {"any": [{"fact": "lstParentAccount", "path": "behaviour", "operator": "isInArray", "value": ["expense", "income"]}]}, "event": [{"type": "update", "params": {"income_type": {"fact": "lstParentAccount", "path": "operating_type"}}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "accounts/postNewGroup", "redirect": "/admin/society/accounts/viewGroups"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/accounts/viewGroups"}, {"title": "Reset", "type": "reset"}, {"title": "Save & Add New", "type": "submit_and_new", "api_path": "accounts/postNewGroup"}], "flow": {"children": {"lstParentAccount": {"title": "Select group", "type": "dropdown", "required": true, "apiPath": "admin/accounts/viewGroups", "labelKeys": ["ledger_account_name"], "allowParentSelection": true, "onMount": true, "dependent": [{"field": "nature_of_account"}, {"field": "behaviour"}, {"field": "operating_type"}]}, "ledger_account_name": {"title": "Group Name", "required": true}, "income_type": {"title": "Income Type", "disabled": true, "required": true}, "nature": {"title": "Nature of Group", "required": true, "default": null, "disabled": true}}}}}