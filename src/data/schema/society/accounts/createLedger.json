{"meta": {"title": "New Ledger"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "lstParentAccount", "path": "id", "operator": "falsy"}]}, "event": [{"type": "schemaOverride", "params": {"nature.enum": []}}, {"type": "uiOverride", "params": {"nature": {"ui:disabled": true, "ui:help": "Please select a group first"}}}]}, {"conditions": {"any": [{"fact": "lstParentAccount", "path": "nature_of_account", "operator": "equal", "value": "dr"}]}, "event": [{"type": "update", "params": {"nature_temp": "dr"}}]}, {"conditions": {"any": [{"fact": "lstParentAccount", "path": "nature_of_account", "operator": "equal", "value": "cr"}]}, "event": [{"type": "update", "params": {"nature_temp": "cr"}}]}, {"conditions": {"any": [{"fact": "lstParentAccount", "path": "nature_of_account", "operator": "notEqual", "value": {"fact": "nature_temp"}}]}, "event": [{"type": "update", "params": {"nature": {"fact": "lstParentAccount", "path": "nature_of_account"}}}]}, {"conditions": {"all": [{"fact": "lstParentAccount", "path": "behaviour", "operator": "equal", "value": "expense"}]}, "event": [{"type": "uiOverride", "params": {"income_type": {"ui:title": "Expense Type"}}}]}, {"conditions": {"any": [{"fact": "lstParentAccount", "path": "behaviour", "operator": "isNotInArray", "value": ["expense", "income"]}]}, "event": [{"type": "remove", "params": {"field": ["income_type"]}}]}, {"conditions": {"any": [{"fact": "lstParentAccount", "path": "behaviour", "operator": "isInArray", "value": ["expense", "income"]}]}, "event": [{"type": "remove", "params": {"field": ["opning_balance"]}}, {"type": "update", "params": {"income_type": {"fact": "lstParentAccount", "path": "operating_type"}}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "accounts/createLedger", "redirect": "/admin/society/accounts/getAllLedgers"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/accounts/getAllLedgers"}, {"title": "Reset", "type": "reset"}, {"title": "Save & New", "type": "submit_and_new", "api_path": "accounts/createLedger"}], "flow": {"children": {"lstParentAccount": {"title": "Select group", "type": "dropdown", "required": true, "apiPath": "admin/accounts/viewGroups", "labelKeys": ["ledger_account_name"], "onMount": true, "dependent": [{"field": "nature_of_account"}, {"field": "behaviour"}, {"field": "operating_type"}]}, "ledger_account_name": {"title": "Ledger Name", "required": true}, "opning_balance": {"title": "Opening Balance", "type": "number"}, "income_type": {"title": "Income Type", "disabled": true}, "nature_temp": {"title": "Nature of Group", "type": "radio", "hidden": true, "enum": [{"title": "Debit", "const": "dr"}, {"title": "Credit", "const": "cr"}], "default": null}, "nature": {"title": "Nature of Group", "type": "radio", "enum": [{"title": "Debit", "const": "dr"}, {"title": "Credit", "const": "cr"}], "default": null}}}}}