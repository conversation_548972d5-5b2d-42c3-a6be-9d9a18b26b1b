{"meta": {"title": "<PERSON>", "data_source": "accounts/getLedgerById/:id"}, "schema": {"rules": [{"conditions": {"all": [{"fact": "behaviour", "operator": "equal", "value": "expense"}]}, "event": [{"type": "uiOverride", "params": {"income_type": {"ui:title": "Expense Type"}}}]}, {"conditions": {"all": [{"fact": "behaviour", "operator": "notIn", "value": ["expense", "income"]}]}, "event": [{"type": "remove", "params": {"field": ["income_type"]}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "accounts/editLedger", "redirect": "/admin/society/accounts/viewLedgers"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/accounts/viewLedgers"}, {"title": "Reset", "type": "reset"}, {"title": "Save & Add New", "type": "submit_and_new", "api_path": "accounts/editLedger"}], "flow": {"children": {"parent_ledger_name": {"title": "Select group", "disabled": true, "required": true, "type": "string"}, "ledger_account_name": {"title": "Ledger Name", "default": "Non occupancy charges", "disabled": true}, "arrAllPreviousFY": {"title": "Opning Balance", "type": "openingBalance"}, "behaviour": {"title": "Behaviour", "hidden": true}, "income_type": {"title": "Income Type", "disabled": true}, "amount_debit_credit": {"title": " Amout Credit/Debit", "enum": ["Credit", "Debit"]}, "nature_of_group": {"title": "Nature of Group", "disabled": true}, "parent_id": {"hidden": true, "type": "number"}}}}}