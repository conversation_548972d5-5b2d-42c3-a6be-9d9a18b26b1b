"use client";

import React, { use, useEffect, useMemo, useState } from 'react'

import { redirect, useSearchParams } from 'next/navigation';

import SaveIcon from '@mui/icons-material/Save'
import CancelIcon from '@mui/icons-material/Cancel'
import { Box, lighten } from '@mui/system';
import { MaterialReactTable } from 'material-react-table';
import axios from 'axios';
import { toast } from 'react-toastify';
import {
  Alert,
  Button,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat'

import { isValidDate } from '@/utils/date';
import { formatCurrency } from '@/utils/string';

dayjs.extend(customParseFormat)

// unchanged helper
const autoClose = Number(process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000);

const commonEditProps = ({ table, cell, handleSaveCell, row, setRowSelection }) => ({
  value: cell.getValue() ?? '',
  variant: "outlined",
  ...(table && cell && {
    onBlur: async (e) => {
      const columnId = cell.column.id;
      const rowId = cell.row.id?.toString();

      if (columnId === "payment_reference") {
        const toastId = toast.loading("Updating reference number...");

        try {
          const updated = await axios.patch(
            `/admin/accounts/bankRecoSaveReferenceNumber`,
            { ref_no: e.target.value, txn_id: rowId },
            {
              baseURL: process.env.NEXT_PUBLIC_API_URL,
              validateStatus: () => true,
              withCredentials: true,
            }
          );

          if (updated.status === 200) {
            toast.update(toastId, {
              render: updated?.data?.message || "Reference number updated successfully",
              type: "success",
              isLoading: false,
              autoClose,
            });
          } else {
            toast.update(toastId, {
              render: updated?.data?.message || "Failed to update reference number",
              type: "error",
              isLoading: false,
              autoClose,
            });
          }
        } catch (error) {
          console.error("Error updating reference number:", error);
          toast.update(toastId, {
            render: "An unexpected error occurred while updating reference number",
            type: "error",
            isLoading: false,
            autoClose,
            closeOnClick: true,
          });
        }
      }

      if (columnId === "value_date") {
        const value = e?.target?.value;

        if (!value) {
          toast.error("Please select a valid date, Entries without a bank date cannot be reconciled", {
            toastId: 'bank-date-required',
            autoClose,
          });

          setRowSelection?.((prev) => ({ ...prev, [rowId]: false }));
          e?.target?.focus();
          table.setEditingCell(rowId, '_error', {
            ...row.original?._error,
            [columnId]: 'This field is required'
          });

          return;
        }

        const selectedDate = new Date(value);
        const transactionDate = new Date(row.original.transaction_date);

        if (selectedDate.getTime() <= transactionDate.getTime()) {
          toast.error("Bank date cannot be before or equal to transaction date", { autoClose });

          return;
        }

        const toastId = toast.loading("Updating bank date...");

        try {
          const updated = await axios.patch(
            `/admin/accounts/bankRecoSaveBankDate`,
            { bank_date: value, txn_id: rowId },
            {
              baseURL: process.env.NEXT_PUBLIC_API_URL,
              validateStatus: () => true,
              withCredentials: true,
            }
          );

          if (updated.status === 200) {
            setRowSelection?.((prev) => ({ ...prev, [rowId]: true }));

            toast.update(toastId, {
              render: updated?.data?.message || "Bank date updated successfully",
              type: "success",
              isLoading: false,
              autoClose,
              closeOnClick: true,
            });
          } else {
            toast.update(toastId, {
              render: updated?.data?.message || "Failed to update bank date",
              type: "error",
              isLoading: false,
              autoClose,
              closeOnClick: true,
            });
          }
        } catch (error) {
          console.error("Error updating bank date:", error);
          toast.update(toastId, {
            render: "An unexpected error occurred while updating bank date",
            type: "error",
            isLoading: false,
            autoClose,
            closeOnClick: true,
          });
        }
      }
    },
    onChange: (e) => {
      handleSaveCell(cell.row.id, cell.column.id, e.target.value);
    },
  }),
});

const Page = () => {
  const [tableData, setTableData] = useState([]);
  const [balanceData, setBalanceData] = useState(null);
  const [rowSelection, setRowSelection] = useState(() => ({}));

  const searchParams = useSearchParams();

  // ─── 1) derive selected rows + sums ─────────────────────────────────────
  const selectedRows = useMemo(
    () =>
      Object.entries(rowSelection)
        .filter(([, sel]) => sel)
        .map(([id]) => tableData.find((r) => r.txn_id.toString() === id))
        .filter(Boolean),
    [rowSelection, tableData]
  );

  const reconciledReceipts = useMemo(
    () => selectedRows.filter((r) => r.voucher_type === 'Receipt')?.reduce((acc, r) => acc + (parseFloat(r?.transaction_amount) || 0), 0),
    [selectedRows]
  );

  const reconciledPayments = useMemo(
    () => selectedRows.filter((r) => r.voucher_type === 'Payment')?.reduce((acc, r) => acc + (parseFloat(r?.transaction_amount) || 0), 0),
    [selectedRows]
  );

  const reconciledClosingBalance = useMemo(() => {
    const openingBalance = parseFloat(balanceData?.reconciled_opening_balance_of) || 0;

    return openingBalance + reconciledReceipts - reconciledPayments;
  }, [balanceData, reconciledReceipts, reconciledPayments]);

  const amountNotReflectedInBank = useMemo(() => {
    const closingBalancePerBankStatement = parseFloat(balanceData?.bankClosingAmount) || 0;

    return reconciledClosingBalance - closingBalancePerBankStatement;
  }, [balanceData, reconciledClosingBalance]);

  // ─── original columns definition, unchanged except we keep all 9 columns ──
  const columns = useMemo(() => {
    return [
      {
        accessorKey: "transaction_date",
        id: "transaction_date",
        header: "Date",
        minSize: 100,
        size: 11.1111,
        grow: true,
        enableColumnActions: false,
        enableSorting: false,
        enableColumnFilter: false,
        enableEditing: false,
        muiTableHeadCellProps: { align: "center" },
        columnDefType: "data",
      },
      {
        accessorKey: "from_account",
        id: "from_account",
        header: "From Account",
        minSize: 100,
        size: 11.1111,
        grow: true,
        enableColumnActions: false,
        enableSorting: false,
        enableColumnFilter: false,
        enableEditing: false,
        muiTableHeadCellProps: { align: "center" },
        columnDefType: "data",
      },
      {
        accessorKey: "memo_desc",
        id: "memo_desc",
        header: "Particulars/Narration",
        minSize: 100,
        size: 11.1111,
        grow: true,
        enableColumnActions: false,
        enableSorting: false,
        enableColumnFilter: false,
        enableEditing: false,
        muiTableHeadCellProps: { align: "center" },
        columnDefType: "data",
      },
      {
        accessorKey: "payment_mode",
        id: "payment_mode",
        header: "Payment Mode",
        minSize: 100,
        size: 11.1111,
        grow: true,
        enableColumnActions: false,
        enableSorting: false,
        enableColumnFilter: false,
        enableEditing: false,
        muiTableHeadCellProps: { align: "center" },
        columnDefType: "data",
      },
      {
        accessorKey: "payment_reference",
        id: "payment_reference",
        header: "Payment Reference",
        minSize: 100,
        size: 11.1111,
        grow: true,
        enableColumnActions: false,
        enableSorting: false,
        enableColumnFilter: false,
        enableEditing: true,
        muiTableHeadCellProps: { align: "center" },
        columnDefType: "data",
        muiEditTextFieldProps: ({ cell, table, row }) => ({
          minLength: 0,
          maxLength: 100,
          ...commonEditProps({ row, table, cell, handleSaveCell }),
        }),
      },
      {
        accessorKey: "value_date",
        id: "value_date",
        header: "Bank Date",
        minSize: 100,
        size: 11.1111,
        grow: true,
        enableColumnActions: false,
        enableSorting: false,
        enableColumnFilter: false,
        enableEditing: true,
        muiTableHeadCellProps: { align: "center" },
        columnDefType: "data",
        muiEditTextFieldProps: ({ row, table, cell }) => {
          const rawMonthYear = searchParams.get('month');
          const monthYear = dayjs(rawMonthYear, ['M-YYYY', 'MMMM YYYY']);
          const monthStart = monthYear.startOf('month');
          const monthEnd = monthYear.endOf('month');

          const otherDate = row?.original?.transaction_date
            ? dayjs(row.original.transaction_date)
            : null;

          const minDate = otherDate
            ? otherDate.isAfter(monthStart) ? otherDate : monthStart
            : monthStart;

          return {
            ...commonEditProps({ row, table, cell, handleSaveCell, setRowSelection }),
            type: 'date',
            inputProps: {
              min: minDate.format('YYYY-MM-DD'),
              max: monthEnd.format('YYYY-MM-DD'),
            },
          };
        },
      },
      {
        accessorKey: "deposit",
        id: "deposit",
        header: "Deposit",
        minSize: 100,
        size: 11.1111,
        grow: true,
        enableColumnActions: false,
        enableSorting: false,
        enableColumnFilter: false,
        enableEditing: false,
        muiTableHeadCellProps: { align: "center" },
        columnDefType: "data",
      },
      {
        accessorKey: "withdrawal",
        id: "withdrawal",
        header: "Withdrawal",
        minSize: 100,
        size: 11.1111,
        grow: true,
        enableColumnActions: false,
        enableSorting: false,
        enableColumnFilter: false,
        enableEditing: false,
        muiTableHeadCellProps: { align: "center" },
        columnDefType: "data",
      },
      {
        accessorKey: "voucher_type",
        id: "voucher_type",
        header: "Type (Payment/Receipt)",
        minSize: 100,
        size: 11.1111,
        grow: true,
        enableColumnActions: false,
        enableSorting: false,
        enableColumnFilter: false,
        enableEditing: false,
        muiTableHeadCellProps: { align: "center" },
        columnDefType: "data",
      },
    ];
  }, [searchParams]);

  const handleCancelReconcillation = async () => {
    redirect("/admin/society/accounts/bankReconciliation");
  }

  const handleConfirmReconcillation = async () => {
    const toastId = toast.loading("Confirming reconciliation...");

    try {
      const response = await axios.put(
        "/admin/accounts/bankReconciliation",
        {
          year: searchParams.get('year'),
          month: searchParams.get('month'),
          bank_ledger_id: searchParams.get('bank_ledger_id'),
          bankClosingAmount: searchParams.get('bankClosingAmount'),
        },
        {
          baseURL: process.env.NEXT_PUBLIC_API_URL,
          validateStatus: () => true,
          withCredentials: true,
        }
      );

      if (response.status === 200) {
        toast.update(toastId, {
          render: response.data?.message || "Reconciliation confirmed successfully",
          type: "success",
          isLoading: false,
          autoClose,
        });

        handleCancelReconcillation();

        // Optionally, refetch data or update state here
      } else {
        toast.update(toastId, {
          render: response.data?.message || "Failed to confirm reconciliation",
          type: "error",
          isLoading: false,
          autoClose,
        });
      }
    } catch (error) {
      console.error("Error confirming reconciliation:", error);

      toast.update(toastId, {
        render: "An error occurred while confirming reconciliation",
        type: "error",
        isLoading: false,
        autoClose,
      });
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      const filters = {
        year: searchParams.get('year'),
        month: searchParams.get('month'),
        bank_ledger_id: searchParams.get('bank_ledger_id'),
        bankClosingAmount: searchParams.get('bankClosingAmount'),
      };

      const toastId = toast.loading("Fetching bank reconciliation data...");

      try {
        const [tableDataRes, balanceDataRes] = await Promise.all([
          axios.get("/admin/accounts/bankReconciliation", {
            params: { filters },
            baseURL: process.env.NEXT_PUBLIC_API_URL,
            validateStatus: () => true,
            withCredentials: true,
          }),
          axios.get("/admin/accounts/bankReconciliationTable", {
            params: { filters },
            baseURL: process.env.NEXT_PUBLIC_API_URL,
            validateStatus: () => true,
            withCredentials: true,
          }),
        ]);

        const errors = [];

        if (tableDataRes.status === 200 && Array.isArray(tableDataRes.data?.data)) {
          const tableRows = tableDataRes.data.data;

          setTableData(tableRows);

          // Auto-select rows with valid bank date
          const initialSelection = tableRows.reduce((acc, row) => {
            if (row?.value_date && isValidDate(row.value_date)) {
              acc[row.txn_id?.toString()] = true;
            }

            return acc;
          }, {});

          setRowSelection(initialSelection);
        } else {
          errors.push(tableDataRes.data?.message || "Failed to fetch transaction data");
        }

        if (balanceDataRes.status === 200 && balanceDataRes.data?.data) {
          if(balanceDataRes.data.data) {
            balanceDataRes.data.data.bankClosingAmount = searchParams.get('bankClosingAmount')
          }

          setBalanceData(balanceDataRes.data.data);
        } else {
          errors.push(balanceDataRes.data?.message || "Failed to fetch balance data");
        }

        if (errors.length > 0) {
          toast.update(toastId, {
            render: errors.join('\n'),
            type: "error",
            isLoading: false,
            autoClose,
            closeOnClick: true,
          });
        } else {
          toast.update(toastId, {
            render: "Bank reconciliation data loaded successfully",
            type: "success",
            isLoading: false,
            autoClose,
            closeOnClick: true,
          });
        }
      } catch (error) {
        console.error("Error fetching data from API:", error);
        toast.update(toastId, {
          render: "An unexpected error occurred while fetching reconciliation data.",
          type: "error",
          isLoading: false,
          autoClose,
          closeOnClick: true,
        });
      }
    };

    fetchData();
  }, [searchParams]);

  const handleSaveCell = (rowId, columnId, value) => {
    if (!rowId || !columnId || value === undefined) {
      console.warn("Invalid parameters for handleSaveCell:", { rowId, columnId, value });

      return;
    }

    setTableData((prevRows) =>
      prevRows.map((row) =>
        row.txn_id === rowId
          ? { ...row, [columnId]: value }
          : row
      )
    );
  };

  const monthDetails = useMemo(() => {
    const month = searchParams.get('month'); // Get the 'month' param from the URL, e.g., '4-2023' for April 2023

    if (!month || !dayjs(month, 'M-YYYY', true).isValid()) return null; // Check if month param exists and is valid using strict parsing

    const monthYear = dayjs(month, 'M-YYYY'); // Parse the month string using Day.js (strict mode)

    return {
      monthNumber: monthYear.month() + 1, // returns 4 for April. Day.js months are 0-indexed internally (0 = Jan, 3 = Apr), so add 1 for human-readable format 
      monthName: monthYear.format('MMMM'), // Full name of the month, e.g., 'April'
      year: monthYear.year(), // Four-digit year, e.g., 2023
      monthYearFull: monthYear.format('MMMM YYYY'), // Combined month and year string, e.g., 'April 2023',
      financialYear: searchParams.get('year'), // Financial year from the URL, e.g., '2023-2024'
    };
  }, [searchParams]);

  return (
    <Grid>
      <MaterialReactTable
        data={tableData}
        columns={columns}
        enableEditing={true}
        editDisplayMode="table"
        getRowId={(row) => row.txn_id?.toString()}
        enablePagination={false}
        state={{
          rowSelection,
          isSaving: false,
        }}
        enableStickyFooter
        enableStickyHeader
        layoutMode="grid"
        renderTopToolbar={() => (
          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid size={12}>
              <Typography variant="h4" gutterBottom>
                Bank Reconciliation for financial year ({monthDetails?.financialYear}) / {monthDetails?.monthYearFull}
              </Typography>
            </Grid>

            <Grid mb={2} gap={2} container>
              <Alert severity="error">
                You can only reconcile transactions with a valid bank date. Ensure the bank date is after the transaction date.
              </Alert>
            </Grid>
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid size={{ xs: 12, md: 4 }}>
                <Paper
                  variant="outlined"
                  sx={{
                    p: 2,
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    textAlign: 'center',
                  }}
                >
                  <Typography variant="subtitle2" gutterBottom>
                    Reconciled Opening Balance of {monthDetails?.monthYearFull}
                  </Typography>
                  <Typography variant="h6" color={balanceData?.reconciled_opening_balance < 0 ? 'error' : 'textPrimary'}>
                    {formatCurrency(balanceData?.reconciled_opening_balance_of)}
                  </Typography>
                </Paper>
              </Grid>

              <Grid size={{ xs: 12, md: 4 }}>
                <Paper
                  variant="outlined"
                  sx={{
                    p: 2,
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    textAlign: 'center',
                  }}
                >
                  <Typography variant="subtitle2" gutterBottom>
                    Opening Balance of {monthDetails?.monthYearFull} by Ledger
                  </Typography>
                  <Typography variant="h6">
                    {formatCurrency(balanceData?.openingBalOfYear)}
                  </Typography>
                </Paper>
              </Grid>

              <Grid size={{ xs: 12, md: 4 }}>
                <Paper
                  variant="outlined"
                  sx={{
                    p: 2,
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    textAlign: 'center',
                  }}
                >
                  <Typography variant="subtitle2" gutterBottom>
                    Bank Account
                  </Typography>
                  <Typography variant="h6" sx={{ wordBreak: 'break-all' }}>
                    {balanceData?.bank_ledger_name} - {searchParams.get('bank_ledger_id')}
                  </Typography>
                </Paper>
              </Grid>
            </Grid>
          </Grid>
        )
        }

        // ─── 2) Selection + guard ────────────────────────────────────────────
        // enableRowSelection={true}

        enableRowSelection={(row) => isValidDate(row?.getValue("value_date"))}
        onRowSelectionChange={setRowSelection}
        muiTableBodyProps={{
          sx: (theme) => ({
            '& tr:nth-of-type(even) > td': {
              backgroundColor: lighten(theme.palette.primary.main, 0.96),
            },
            '& tr:nth-of-type(odd) > td': {
              backgroundColor: lighten(theme.palette.background.paper, 0.04),
            },
            '& tr:hover > td': {
              backgroundColor: lighten(theme.palette.primary.main, 0.9),
            },
          }),
        }}
        muiTableContainerProps={{
          sx: {
            height: 'auto',
            overflowY: 'auto',
            overflowX: 'hidden',
          },
        }}
      />

      {/* ─── Bottom summary table ───────────────────────────────────────────── */}
      <TableContainer component={Paper} sx={{ mt: 2 }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>
                <strong>
                  Bank A/C: {balanceData?.bank_ledger_name} -{' '}
                  {searchParams.get('bank_ledger_id')}
                </strong>
              </TableCell>
              <TableCell align="right">
                <strong>Amount</strong>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {monthDetails?.monthNumber && monthDetails?.monthNumber === 4 && (
              <TableRow>
                <TableCell>Opening Balance</TableCell>
                <TableCell align="right">
                  {formatCurrency(balanceData?.reconciled_opening_balance_of)}
                </TableCell>
              </TableRow>
            )}
            <TableRow>
              <TableCell>Reconciled Receipts</TableCell>
              <TableCell align="right">
                {formatCurrency(reconciledReceipts)}
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Reconciled Payments</TableCell>
              <TableCell align="right">
                {formatCurrency(reconciledPayments)}
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Reconciled Closing Balance</TableCell>
              <TableCell align="right">
                {formatCurrency(reconciledClosingBalance)}
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Closing Balance (As Per Bank Statement)</TableCell>
              <TableCell align="right">
                {formatCurrency(balanceData?.bankClosingAmount || 0)}
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell>
                <strong>Amount Not Reflected in Bank</strong>
              </TableCell>
              <TableCell align="right">
                <strong>{formatCurrency(amountNotReflectedInBank)}</strong>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>

      <Box
        display="flex"
        justifyContent="flex-end"
        alignItems="center"
        gap={2}
        mt={2}
      >
        <Button
          variant="contained"
          startIcon={<SaveIcon />}
          onClick={handleConfirmReconcillation}
        >
          Confirm Reconcile
        </Button>
        <Button
          variant="outlined"
          color="error"
          startIcon={<CancelIcon />}
          onClick={handleCancelReconcillation}
        >
          Cancel
        </Button>
      </Box>
    </Grid >
  )
}

export default Page
