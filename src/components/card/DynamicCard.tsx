// @ts-nocheck

'use client'

import { useState, useEffect, Fragment, useCallback, useMemo, memo, useRef, Component, type FC } from 'react'

// FC type removed as it was unused

import { usePathname, useRouter } from 'next/navigation'

import {
  Card,
  CardContent,
  CardHeader,
  Grid,
  Typography,
  Box,
  Checkbox,
  Pagination,
  PaginationItem,
  Button,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton
} from '@mui/material'
import ErrorIcon from '@mui/icons-material/Error'
import InfoIcon from '@mui/icons-material/Info'
import CloseIcon from '@mui/icons-material/Close'
import axios from 'axios'

import { toast } from 'react-toastify'

import { Icon } from '@iconify/react/dist/iconify.js'

import StatusChip from '../chip/StatusChip'
import { concatenateKeys, renderValue } from '../../@menu/utils/renderValue'
import { API_ENDPOINTS } from '../../configs/apiEndpoints'
import { replacePlaceholders } from '@/utils/replacePlaceholders'
import { stripAdminScopedPath } from '@/utils/string'

type KeyDisplay = {
  key: string | string[]
  label: string
  keyGrid: number
  valueGrid: number
  showAsZero?: boolean
  showAsYesNo?: boolean
  showAsStatus?: boolean
  displayMap?: Record<string | number, string>
  showAsChip?: boolean
  isLink: boolean
  redirectUrl?: string // URL for internal page redirects when isLink is true
}

type HeaderKeyDisplay = {
  key: string
  label: string
  isLink?: boolean
  isCheckbox?: boolean
  showChip?: boolean
  chipColor?: 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning' | string
  showSubHeader?: boolean
  displayMap?: Record<string | number, string>
  showIcon?: boolean
  redirectUrl?: string
  IconClassName?: string
  isApiCall?: boolean // New: if true, make API call instead of redirect
  httpMethod?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' // New: HTTP method for API calls

  // New: support multiple chips
  chips?: Array<{
    key: string
    statusType?: 'status' | 'incidental' | 'nonmember'
    fallbackLabel?: string
  }>
}

type MainGridConfig = {
  xs?: number
  sm: number
  md?: number
  lg?: number
}

type DynamicCardProps = {
  keyDisplay: KeyDisplay[]
  mainGrid?: MainGridConfig
  headerKeyDisplay?: HeaderKeyDisplay[]
  customUrl?: string
  renderButtonGroup?: boolean
  onDataFetch?: (data: any[]) => void
}

type TableData = {
  item_name: string
  item_cost: number
  item_quantity: number
  total_cost: number
}

type ResponseData = {
  data: any[]
  meta: { pagination?: { total: number } }
}

// Custom hook to prevent unnecessary rerenders
function useStableProps<T>(props: T): T {
  const stablePropsRef = useRef<T>(props)

  // Only update the ref if critical props change
  useEffect(() => {
    const criticalProps = ['customUrl', 'currentPage', 'mainGrid']
    let shouldUpdate = false

    for (const prop of criticalProps) {
      if (props[prop] !== stablePropsRef.current[prop]) {
        shouldUpdate = true
        break
      }
    }

    if (shouldUpdate) {
      stablePropsRef.current = props
    }
  }, [props])

  return stablePropsRef.current
}

// Define a custom comparison function for React.memo
const arePropsEqual = (prevProps: DynamicCardProps, nextProps: DynamicCardProps) => {
  // Only rerender if these specific props change
  const criticalProps = ['customUrl', 'currentPage', 'mainGrid']

  // Check if any critical prop has changed
  for (const prop of criticalProps) {
    if (prevProps[prop] !== nextProps[prop]) {
      return false // Props are not equal, should rerender
    }
  }

  // Special check for mainGrid since it's an object
  if (prevProps.mainGrid !== nextProps.mainGrid) {
    // If either is undefined and the other isn't, they're different
    if (!prevProps.mainGrid || !nextProps.mainGrid) {
      return false
    }

    // Compare individual properties
    if (
      prevProps.mainGrid.sm !== nextProps.mainGrid.sm ||
      prevProps.mainGrid.md !== nextProps.mainGrid.md ||
      prevProps.mainGrid.lg !== nextProps.mainGrid.lg ||
      prevProps.mainGrid.xs !== nextProps.mainGrid.xs
    ) {
      return false
    }
  }

  // Props are equal, no need to rerender
  return true
}

// Use React.memo with custom comparison function
const DynamicCard: FC<DynamicCardProps> = memo(props => {
  // Log the original props
  // Use stable props to prevent unnecessary rerenders
  const {
    keyDisplay,
    mainGrid: rawMainGrid = { sm: 12, md: 6, lg: 6, xs: 12 }, // Default values
    headerKeyDisplay,
    customUrl,
    renderButtonGroup = false,
    onDataFetch
  } = useStableProps(props)

  // Ensure all grid properties are set - wrapped in useMemo to prevent unnecessary rerenders
  const mainGrid = useMemo(
    () => ({
      sm: rawMainGrid.sm || 12, // Ensure sm is always set
      md: rawMainGrid.md || 6,
      lg: rawMainGrid.lg || 6,
      xs: rawMainGrid.xs || 12
    }),
    [rawMainGrid.sm, rawMainGrid.md, rawMainGrid.lg, rawMainGrid.xs]
  )

  // Log the extracted mainGrid prop
  // console.log('DynamicCard mainGrid after extraction:', mainGrid);

  const pathname = usePathname()
  const asPath = stripAdminScopedPath(pathname)
  const router = useRouter()

  // Debug counter to track rerenders
  const renderCountRef = useRef(0)

  renderCountRef.current += 1

  const [data, setData] = useState<any[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [selectedIds, setSelectedIds] = useState<Set<number>>(new Set())
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [totalPages, setTotalPages] = useState<number>(1)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false)
  const [deleteAction, setDeleteAction] = useState<{ url: string; item: any } | null>(null)

  // Use this function instead of direct state updates to control rerenders
  const updateState = useCallback(updater => {
    updater()
  }, [])

  // Use refs to store the latest props to prevent unnecessary rerenders
  const onDataFetchRef = useRef(onDataFetch)
  const keyDisplayRef = useRef(keyDisplay)
  const headerKeyDisplayRef = useRef(headerKeyDisplay)
  const customUrlRef = useRef(customUrl)

  // Update the refs whenever props change
  useEffect(() => {
    onDataFetchRef.current = onDataFetch
    keyDisplayRef.current = keyDisplay
    headerKeyDisplayRef.current = headerKeyDisplay
    customUrlRef.current = customUrl
  }, [onDataFetch, keyDisplay, headerKeyDisplay, customUrl])

  const pageSize = 10

  const normalizeData = useCallback(
    (responseData: ResponseData) => {
      const { data = [], meta = {} } = responseData || {}
      const totalPages = Math.ceil((meta.pagination?.total || 0) / pageSize) || 1

      return {
        items: Array.isArray(data) ? data : [data],
        totalPages
      }
    },
    [pageSize]
  )

  const fetchData = useCallback(async () => {
    updateState(() => setLoading(true))

    // Clean URL - remove /admin/society/ or /admin/gate/ prefix if present
    let cleanUrl = customUrlRef.current || asPath

    // Remove /admin/society/ or /admin/gate/ from the beginning
    cleanUrl = cleanUrl.replace(/^\/admin\/(society|gate)\//, '/admin/')

    // Debug logging
    console.log('🔥 DynamicCard fetchData Debug:', {
      originalUrl: customUrlRef.current || asPath,
      cleanedUrl: cleanUrl,
      baseURL: process.env.NEXT_PUBLIC_API_URL,
      fullUrl: `${process.env.NEXT_PUBLIC_API_URL}${cleanUrl}`
    });

    try {
      const response = await axios.get(cleanUrl, {
        baseURL: process.env.NEXT_PUBLIC_API_URL,
        params: { page: currentPage, page_size: pageSize },
        withCredentials: true
      })

      if (response.status === 200 && response.data?.data) {
        const normalizedData = normalizeData(response.data)

        // Use batch updates to prevent multiple rerenders
        updateState(() => {
          setData(normalizedData.items)
          setTotalPages(normalizedData.totalPages)
        })

        // Use the ref to access the latest onDataFetch callback
        // This prevents the fetchData function from being recreated when onDataFetch changes
        if (onDataFetchRef.current && normalizedData.items.length > 0) {
          // Only call onDataFetch if we have items to prevent unnecessary rerenders
          onDataFetchRef.current(normalizedData.items)
        }
      } else {
        toast.error('No Response: Provide data from backend side is null')
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('Error fetching data')
    } finally {
      updateState(() => setLoading(false))
    }
  }, [currentPage, normalizeData, pageSize, updateState, asPath])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  const handleCheckboxChange = useCallback(
    (id: number) => {
      updateState(() => {
        setSelectedIds(prev => {
          const newSet = new Set(prev)

          if (newSet.has(id)) {
            newSet.delete(id)
          } else {
            newSet.add(id)
          }

          return newSet
        })
      })
    },
    [updateState]
  )

  /**
   * Handle redirect navigation (original functionality)
   */
  const handleRedirect = useCallback(
    (redirectUrl: string, item?: any) => {
      console.log('handleRedirect - redirectUrl:', redirectUrl)
      console.log('handleRedirect - item:', item)

      let dataForReplacement = item || {}

      // Try to find ID from various possible field names
      const possibleIdFields = ['id', 'purchase_form_id', 'purchase_id', '_id', 'account_id']
      let foundId = null

      for (const field of possibleIdFields) {
        if (dataForReplacement[field]) {
          foundId = dataForReplacement[field]
          console.log(`handleRedirect - found ID in field '${field}':`, foundId)
          break
        }
      }

      // If no ID found in item, try to extract from current pathname
      if (!foundId && typeof window !== 'undefined') {
        const pathname = window.location.pathname
        const idMatch = pathname.match(/\/(\d+)(?:\/|$)/)

        if (idMatch) {
          foundId = idMatch[1]
          console.log('handleRedirect - extracted ID from pathname:', foundId)
        }
      }

      // Ensure we have an id field for replacement
      if (foundId) {
        dataForReplacement = { ...dataForReplacement, id: foundId }
      }

      console.log('handleRedirect - dataForReplacement:', dataForReplacement)

      const url = replacePlaceholders(redirectUrl, dataForReplacement)

      console.log('handleRedirect - final URL:', url)

      router.push(url)
    },
    [router]
  )

  /**
   * Handle icon click with API call functionality:
   * Instead of redirecting, make an API call to the redirectUrl
   */

  const handleIconClick = useCallback(async (redirectUrl: string, item?: any, httpMethod: string = 'GET') => {
    console.log('handleIconClick - redirectUrl:', redirectUrl)
    console.log('handleIconClick - item:', item)
    console.log('handleIconClick - httpMethod:', httpMethod)

    // Show confirmation dialog for DELETE operations
    if (httpMethod.toUpperCase() === 'DELETE') {
      let dataForReplacement = item || {}

      // Try to find ID from various possible field names
      const possibleIdFields = ['id', 'purchase_form_id', 'purchase_id', '_id', 'account_id']
      let foundId = null

      for (const field of possibleIdFields) {
        if (dataForReplacement[field]) {
          foundId = dataForReplacement[field]
          console.log(`handleIconClick - found ID in field '${field}':`, foundId)
          break
        }
      }

      // If no ID found in item, try to extract from current pathname
      if (!foundId && typeof window !== 'undefined') {
        const pathname = window.location.pathname
        const idMatch = pathname.match(/\/(\d+)(?:\/|$)/)

        if (idMatch) {
          foundId = idMatch[1]
          console.log('handleIconClick - extracted ID from pathname:', foundId)
        }
      }

      // Ensure we have an id field for replacement
      if (foundId) {
        dataForReplacement = { ...dataForReplacement, id: foundId }
      }

      const url = replacePlaceholders(redirectUrl, dataForReplacement)

      // Store the delete action and open dialog
      setDeleteAction({ url, item: dataForReplacement })
      setDeleteDialogOpen(true)

      return
    }

    // Continue with non-DELETE operations
    let dataForReplacement = item || {}

    // Try to find ID from various possible field names
    const possibleIdFields = ['id', 'purchase_form_id', 'purchase_id', '_id', 'account_id']
    let foundId = null

    for (const field of possibleIdFields) {
      if (dataForReplacement[field]) {
        foundId = dataForReplacement[field]
        console.log(`handleIconClick - found ID in field '${field}':`, foundId)
        break
      }
    }

    // If no ID found in item, try to extract from current pathname
    if (!foundId && typeof window !== 'undefined') {
      const pathname = window.location.pathname
      const idMatch = pathname.match(/\/(\d+)(?:\/|$)/)

      if (idMatch) {
        foundId = idMatch[1]
        console.log('handleIconClick - extracted ID from pathname:', foundId)
      }
    }

    // Ensure we have an id field for replacement
    if (foundId) {
      dataForReplacement = { ...dataForReplacement, id: foundId }
    }

    console.log('handleIconClick - dataForReplacement:', dataForReplacement)

    const url = replacePlaceholders(redirectUrl, dataForReplacement)

    console.log('handleIconClick - final URL:', url)

    try {
      // Prepare axios config based on HTTP method
      const axiosConfig = {
        baseURL: process.env.NEXT_PUBLIC_API_URL,
        withCredentials: true,
        responseType: 'blob' as const // Handle different response types (PDF, etc.)
      }

      let response

      // Make API call with the specified HTTP method
      switch (httpMethod.toUpperCase()) {
        case 'GET':
          response = await axios.get(url, axiosConfig)
          break
        case 'POST':
          response = await axios.post(url, {}, axiosConfig)
          break
        case 'PUT':
          response = await axios.put(url, {}, axiosConfig)
          break
        case 'DELETE':
          response = await axios.delete(url, axiosConfig)
          break
        case 'PATCH':
          response = await axios.patch(url, {}, axiosConfig)
          break
        default:
          console.warn(`Unsupported HTTP method: ${httpMethod}, defaulting to GET`)
          response = await axios.get(url, axiosConfig)
      }

      console.log('handleIconClick - API response:', response)

      // Check if the response is a file (PDF, etc.)
      const contentType = response.headers['content-type'] || response.headers['Content-Type']

      if (contentType && (contentType.includes('pdf') || contentType.includes('octet-stream'))) {
        // Handle file downloads
        const blob = new Blob([response.data], { type: contentType })
        const downloadUrl = window.URL.createObjectURL(blob)
        const link = document.createElement('a')

        link.href = downloadUrl

        // Extract filename from Content-Disposition header or use default
        const contentDisposition = response.headers['content-disposition'] || response.headers['Content-Disposition']
        let filename = 'download'

        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)

          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1].replace(/['"]/g, '')
          }
        }

        link.download = filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(downloadUrl)

        toast.success('File downloaded successfully')
      } else {
        // Handle JSON responses
        let responseMessage = 'Action completed successfully'

        // Customize message based on HTTP method
        switch (httpMethod.toUpperCase()) {
          case 'DELETE':
            responseMessage = 'Item deleted successfully'
            break
          case 'POST':
            responseMessage = 'Item created successfully'
            break
          case 'PUT':
          case 'PATCH':
            responseMessage = 'Item updated successfully'
            break
        }

        toast.success(responseMessage)

        // Refresh the data after successful API call
        // This will re-fetch the list to show updated data
        setTimeout(() => {
          window.location.reload()
        }, 1000)
      }
    } catch (error) {
      console.error('handleIconClick - API error:', error)

      if (error.response?.status === 404) {
        toast.error('Resource not found')
      } else if (error.response?.status === 403) {
        toast.error('Access denied')
      } else if (error.response?.status >= 500) {
        toast.error('Server error occurred')
      } else {
        toast.error('API call failed')
      }
    }
  }, [])

  // Handle delete confirmation dialog
  const handleDeleteCancel = useCallback(() => {
    setDeleteDialogOpen(false)
    setDeleteAction(null)
  }, [])

  const handleDeleteConfirm = useCallback(async () => {
    if (!deleteAction) return

    try {
      setDeleteDialogOpen(false)

      const response = await axios.delete(deleteAction.url, {
        baseURL: process.env.NEXT_PUBLIC_API_URL,
        withCredentials: true
      })

      console.log('Delete API response:', response)
      toast.success('Item deleted successfully')

      // Refresh the data after successful deletion
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    } catch (error) {
      console.error('Delete API error:', error)

      if (error.response?.status === 404) {
        toast.error('Resource not found')
      } else if (error.response?.status === 403) {
        toast.error('Access denied')
      } else if (error.response?.status >= 500) {
        toast.error('Server error occurred')
      } else {
        toast.error('Delete operation failed')
      }
    } finally {
      setDeleteAction(null)
    }
  }, [deleteAction])

  // Helper function to determine if a URL is external
  const isExternalUrl = useCallback((url: string) => {
    return (
      url.startsWith('http://') || url.startsWith('https://') || url.startsWith('mailto:') || url.startsWith('tel:')
    )
  }, [])

  // Handle link click - supports both internal redirects and external links
  const handleLinkClick = useCallback(
    (url: string, redirectUrl: string | undefined, item: any) => {
      if (redirectUrl) {
        // Use handleRedirect for consistent navigation with placeholder replacement
        handleRedirect(redirectUrl, item)
      } else if (isExternalUrl(url)) {
        // Open external URLs in new tab
        window.open(url, '_blank', 'noopener,noreferrer')
      } else {
        // Handle internal URLs without placeholder replacement
        router.push(url)
      }
    },
    [handleRedirect, isExternalUrl, router]
  )

  const handlePageChange = useCallback(
    (_: React.ChangeEvent<unknown>, value: number) => {
      updateState(() => setCurrentPage(value))
    },
    [updateState]
  )

  const handleAction = useCallback(
    async (action: string) => {
      if (selectedIds.size === 0) {
        toast.warn('Please select at least one item')

        return
      }

      try {
        const response = await axios.put(
          `${process.env.NEXT_PUBLIC_API_URL}/${API_ENDPOINTS[action]}`,
          { id: Array.from(selectedIds) },
          {
            withCredentials: true
          }
        )

        response.data?.data?.forEach(({ status, message }: { status: string; message: string }) => {
          if (status === 'error') {
            toast.error(message)
          } else {
            toast.success(message)
          }
        })

        router.refresh()
      } catch (error) {
        console.error('Error performing action:', error)
        toast.error('Action failed')
      }
    },
    [selectedIds, router]
  )

  const renderHeaderContent = useCallback(
    (item: any) => (
      <Box
        display='flex'
        justifyContent='space-between'
        alignItems='flex-start'
        sx={{
          flexDirection: { xs: 'column', md: 'row' },
          gap: { xs: 2, md: 0 }
        }}
      >
        <Box
          display='flex'
          alignItems='flex-start'
          sx={{
            flex: 1,
            minWidth: 0,
            flexDirection: 'column',
            gap: 0.5
          }}
        >
          {headerKeyDisplayRef.current?.map(
            ({ key, label, isLink, isCheckbox, showSubHeader, subHeaderInline, displayMap, redirectUrl, showChip }) => {
              // Only render non-chip elements in the left section
              if (showChip) return null

              return (
                <Fragment key={`header-${key}`}>
                  <Box display='flex' alignItems='center' sx={{ width: '100%' }}>
                    {isCheckbox && (
                      <Checkbox
                        onChange={() => handleCheckboxChange(item.id)}
                        disabled={item.disable === 'true'}
                        checked={selectedIds.has(item.id)}
                        sx={{ mr: 1 }}
                      />
                    )}
                    <Box sx={{
                      display: 'flex',
                      flexDirection: showSubHeader && !subHeaderInline ? 'column' : 'row',
                      alignItems: showSubHeader && !subHeaderInline ? 'flex-start' : 'center',
                      gap: showSubHeader && !subHeaderInline ? 0.25 : 0.5,
                      flexWrap: 'wrap'
                    }}>
                      <Typography
                        variant='h6'
                        component='div'
                        sx={{
                          fontWeight: 600,
                          fontSize: '1.125rem',
                          color: 'text.primary',
                          lineHeight: 1.2,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5,
                          flexWrap: 'wrap'
                        }}
                      >
                        {showSubHeader && subHeaderInline ? (
                          <>
                            <span>{label}</span>
                            <span style={{ marginLeft: 8 }}>{displayMap?.[item[key]] ?? item[key]}</span>
                          </>
                        ) : showSubHeader && !subHeaderInline ? (
                          <span style={{ display: 'none' }}>{label}</span>
                        ) : (
                          <>
                            <span>{label}</span>
                            {item[key] !== 0 &&
                              !!item[key] &&
                              (isLink ? (
                                <Box
                                  component='span'
                                  onClick={() => {
                                    handleLinkClick(item[key], redirectUrl, item)
                                  }}
                                  sx={{
                                    color: 'primary.main',
                                    cursor: 'pointer',
                                    '&:hover': { color: 'primary.dark' }
                                  }}
                                >
                                  {item[key]}
                                </Box>
                              ) : (
                                <Box
                                  component='span'
                                  sx={{
                                    color: 'text.primary',
                                    cursor: 'default'
                                  }}
                                >
                                  {item[key]}
                                </Box>
                              ))}
                          </>
                        )}
                      </Typography>

                      {/* Inline Sub-header */}
                      {showSubHeader && subHeaderInline && (
                        <Typography
                          variant='subtitle2'
                          color='text.secondary'
                          sx={{
                            fontSize: '0.8125rem',
                            fontWeight: 400,
                            lineHeight: 1.3,
                            color: 'rgba(0, 0, 0, 0.6)',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 0.5
                          }}
                        >
                          <span>{label}</span>
                          <span>{displayMap?.[item[key]] ?? item[key]}</span>
                        </Typography>
                      )}
                    </Box>
                  </Box>
                  {/* Block Sub-header (below header) */}
                  {showSubHeader && !subHeaderInline && (
                    <Typography
                      variant='subtitle2'
                      color='text.secondary'
                      sx={{
                        fontSize: '0.8125rem',
                        fontWeight: 400,
                        lineHeight: 1.3,
                        ml: isCheckbox ? 5 : 0,
                        mt: 0.25,
                        color: 'rgba(0, 0, 0, 0.6)',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5
                      }}
                    >
                      <span>{label}</span>
                      <span>{displayMap?.[item[key]] ?? item[key]}</span>
                    </Typography>
                  )}
                </Fragment>
              )
            }
          )}
        </Box>
        <Box
          display='flex'
          alignItems='flex-start'
          sx={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            gap: 1,
            justifyContent: { xs: 'flex-start', md: 'flex-end' },
            maxWidth: { xs: '100%', md: '60%' },
            minWidth: 0
          }}
        >
          {/* Render all chips in a single wrapping container */}
          {headerKeyDisplayRef.current?.map(({ chips }) =>
            chips && chips.length > 0 ? (
              chips.map((chip, chipIdx) =>
                item[chip.key] ? (
                  <StatusChip
                    key={`multi-chip-${chip.key}-${chipIdx}`}
                    status={item[chip.key]}
                    statusType={chip.statusType}
                    fallbackLabel={chip.fallbackLabel || item[chip.key]}
                    sx={{
                      background: 'transparent',
                      color: '#4caf50',
                      fontWeight: 600,
                      px: 1.5,
                      minHeight: 26,
                      fontSize: 12,
                      boxShadow: 'none',
                      border: '1.5px solid #4caf50',
                      margin: 0.5,
                      '& .MuiChip-label': {
                        color: '#4caf50',
                        fontWeight: 600,
                        fontSize: 12
                      }
                    }}
                  />
                ) : null
              )
            ) : null
          )}
          {headerKeyDisplayRef.current?.map(({ showChip, key }, index) =>
            showChip ? (
              <StatusChip
                key={`single-chip-${key}-${index}`}
                status={item[key]}
                fallbackLabel={item[key]}
                color={headerKeyDisplayRef.current[index]?.chipColor || 'success'}
                sx={{
                  px: 1.5,
                  border: '1.5px solid #4caf50',
                  background: 'transparent',
                  fontWeight: 600,
                  fontSize: 12,
                  minHeight: 26,
                  boxShadow: 'none',
                  margin: 0.5,
                  '& .MuiChip-label': {
                    fontWeight: 600,
                    fontSize: 12
                  }
                }}
              />
            ) : null
          )}
          {headerKeyDisplayRef.current?.map(
            ({ showIcon, redirectUrl, IconClassName, key, isApiCall, httpMethod }, index) =>
              showIcon ? (
                <Icon
                  key={`icon-${key || index}`}
                  icon={IconClassName}
                  width={20}
                  height={20}
                  style={{ cursor: 'pointer', marginLeft: '8px', color: '#0a4f92' }}
                  onClick={() => {
                    if (isApiCall) {
                      handleIconClick(redirectUrl || '', item, httpMethod || 'GET')
                    } else {
                      handleRedirect(redirectUrl || '', item)
                    }
                  }}
                />
              ) : null
          )}
        </Box>
      </Box>
    ),
    [handleCheckboxChange, selectedIds, handleIconClick, handleLinkClick, handleRedirect]
  )

  const renderTable = useCallback(
    (tableData: TableData[]) => (
      <TableContainer component={Paper} sx={{ mt: 2 }}>
        <Table>
          <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
            <TableRow>
              <TableCell align='center' sx={{ fontWeight: 'bold' }}>
                Item Name
              </TableCell>
              <TableCell align='center' sx={{ fontWeight: 'bold' }}>
                Unit Cost (₹)
              </TableCell>
              <TableCell align='center' sx={{ fontWeight: 'bold' }}>
                Item Quantity
              </TableCell>
              <TableCell align='center' sx={{ fontWeight: 'bold' }}>
                Total Cost (₹)
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {tableData.length > 0 ? (
              tableData.map((row, index) => (
                <TableRow key={`table-row-${index}`}>
                  <TableCell align='center'>{row.item_name}</TableCell>
                  <TableCell align='center'>{row.item_cost}</TableCell>
                  <TableCell align='center'>{row.item_quantity}</TableCell>
                  <TableCell align='center'>{row.total_cost}</TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell align='center' colSpan={4}>
                  No Record Found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    ),
    []
  )

  // Using empty dependency array as concatenateKeys and renderValue are imported functions
  const renderCardContent = useCallback(
    (item: any) => (
      <>
        <CardContent>
          <Grid container spacing={2}>
            {keyDisplayRef.current.map(
              ({
                key,
                label,
                keyGrid,
                valueGrid,
                showAsStatus,
                showAsYesNo,
                displayMap,
                isLink = false,
                redirectUrl,
                suffix // <-- ensure suffix is destructured
              }) => {
                let value

                if (Array.isArray(key)) {
                  value = concatenateKeys(item, key)
                } else if (showAsStatus) {
                  value = renderValue(item, key, { showAsStatus })
                } else if (showAsYesNo) {
                  value = renderValue(item, key, { showAsYesNo })
                } else if (displayMap) {
                  value = displayMap[item[key]] || item[key]
                } else {
                  value = item[key]
                }

                // Use suffix property for appending units (standardized)

                let displayValue

                if (value === 0) {
                  displayValue = typeof suffix === 'string' && suffix ? `0 ${suffix}` : 0
                } else if (value === '') {
                  displayValue = '-'
                } else if (value == null) {
                  displayValue = 'NA'
                } else if (typeof suffix === 'string' && suffix) {
                  displayValue = `${value} ${suffix}`
                } else {
                  displayValue = value
                }

                return (
                  <Fragment key={Array.isArray(key) ? key.join(',') : key}>
                    <Grid size={keyGrid}>
                      <Typography variant='body2' sx={{ color: 'grey.700' }}>
                        <strong>{label}:</strong>
                      </Typography>
                    </Grid>
                    <Grid size={valueGrid}>
                      {isLink ? (
                        <Box
                          component='span'
                          onClick={() => handleLinkClick(displayValue, redirectUrl, item)}
                          sx={{
                            color: 'primary.main',
                            cursor: 'pointer',
                            '&:hover': {
                              color: 'primary.dark'
                            }
                          }}
                        >
                          {displayValue}
                        </Box>
                      ) : (
                        <Typography
                          variant='body2'
                          sx={{
                            color: 'grey.900',
                            wordWrap: 'break-word',
                            whiteSpace: 'normal',
                            overflowWrap: 'anywhere',
                            maxWidth: '100%',
                            display: 'block'
                          }}
                        >
                          {displayValue}
                        </Typography>
                      )}
                    </Grid>
                  </Fragment>
                )
              }
            )}
          </Grid>
        </CardContent>
      </>
    ),
    [handleLinkClick]
  ) // Removed concatenateKeys and renderValue from dependencies as they are imported functions

  const ButtonGroup = memo(
    ({ isButton = false, handleAction }: { isButton: boolean; handleAction: (action: string) => void }) => {
      if (!isButton) return null

      return (
        <Box>
          {['approve', 'reject', 'review', 'refuse'].map(action => (
            <Button
              key={`action-${action}`}
              variant='contained'
              color={action === 'approve' || action === 'review' ? 'success' : 'error'}
              sx={{ mr: 2, mb: 2 }}
              onClick={() => handleAction(action)}
            >
              {`${action.charAt(0).toUpperCase() + action.slice(1)} Selected`}
            </Button>
          ))}
        </Box>
      )
    }
  )

  // Memoize the card content to prevent unnecessary recalculations
  const cardContent = useMemo(() => {
    // Debug mainGrid values
    // console.log('DynamicCard mainGrid values:', {
    //   mainGrid,
    //   sm: mainGrid.sm,
    //   md: mainGrid.md,
    //   lg: mainGrid.lg,
    //   xs: mainGrid.xs
    // });

    if (loading) {
      return (
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <Skeleton variant='rectangular' width={530} height={118} />
          </Box>
        </Grid>
      )
    }

    if (data.length === 0) {
      return (
        <Grid item xs={12}>
          <Card sx={{ textAlign: 'center', p: 2, boxShadow: 3 }}>
            <CardContent>
              <ErrorIcon color='error' fontSize='large' />
              <Typography variant='h6' color='error' mt={2}>
                No data found
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      )
    }

    return data.map((item, index) => (
      <Grid
        item
        key={`card-grid-${index}`}
        xs={mainGrid.xs}
        sm={mainGrid.sm}
        md={mainGrid.md}
        lg={mainGrid.lg}
        sx={{
          padding: 1,
          boxSizing: 'border-box'
        }}
      >
        <Card
          sx={{
            position: 'relative',
            mb: 3,
            boxShadow: 3,
            borderRadius: 2,
            ':hover': {
              boxShadow: 6,
              transform: 'scale(1.01)',
              transition: '0.3s ease-in-out'
            }
          }}
        >
          <CardHeader
            title={renderHeaderContent(item)}
            sx={{ backgroundColor: 'grey.100', borderBottom: '1px solid #ddd', mb: 2 }}
          />
          {renderCardContent(item)}
        </Card>
        <Box
          key={`table-box-${index}`}
          sx={{
            position: 'relative',
            mb: 3,
            boxShadow: 3,
            borderRadius: 2,
            ':hover': {
              boxShadow: 6,
              transform: 'scale(1.01)',
              transition: '0.3s ease-in-out'
            }
          }}
        >
          {item.purchase_form_items && renderTable(item.purchase_form_items)}
        </Box>
      </Grid>
    ))
  }, [
    data,
    loading,
    mainGrid.sm,
    mainGrid.md,
    mainGrid.lg,
    mainGrid.xs,
    renderTable,
    renderHeaderContent,
    renderCardContent
  ])

  // Memoize the pagination component
  const paginationComponent = useMemo(() => {
    if (totalPages <= 1) return null

    return (
      <Pagination
        count={totalPages}
        page={currentPage}
        onChange={handlePageChange}
        shape='rounded'
        color='primary'
        renderItem={item => <PaginationItem {...item} />}
      />
    )
  }, [totalPages, currentPage, handlePageChange])

  return (
    <>
      <Grid
        container
        spacing={3}
        sx={{
          mt: 2,
          width: '100%',
          display: 'flex',
          flexWrap: 'wrap',
          padding: 2,
          boxSizing: 'border-box'
        }}
      >
        {cardContent}
      </Grid>

      <Box display='flex' justifyContent='space-between' alignItems='center' mt={4}>
        {useMemo(
          () => (
            <ButtonGroup isButton={renderButtonGroup} handleAction={handleAction} />
          ),
          [renderButtonGroup, handleAction, ButtonGroup]
        )}
        {paginationComponent}
      </Box>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        aria-labelledby='delete-dialog-title'
        aria-describedby='delete-dialog-description'
        maxWidth='sm'
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: '8px',
            padding: '16px'
          }
        }}
      >
        <DialogTitle
          id='delete-dialog-title'
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            padding: '16px 24px',
            fontSize: '18px',
            fontWeight: 500
          }}
        >
          <InfoIcon
            sx={{
              color: '#2196f3',
              fontSize: '24px'
            }}
          />
          Delete
          <Box sx={{ flex: 1 }} />
          <IconButton
            onClick={handleDeleteCancel}
            size='small'
            sx={{
              color: '#666',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)'
              }
            }}
          >
            <CloseIcon fontSize='small' />
          </IconButton>
        </DialogTitle>
        <DialogContent
          id='delete-dialog-description'
          sx={{
            padding: '0 24px 16px 24px',
            fontSize: '14px',
            color: '#333'
          }}
        >
          Are you sure you want to proceed?
        </DialogContent>
        <DialogActions
          sx={{
            padding: '0 24px 16px 24px',
            gap: 1
          }}
        >
          <Button
            onClick={handleDeleteCancel}
            variant='outlined'
            sx={{
              textTransform: 'none',
              borderColor: '#ddd',
              color: '#666',
              '&:hover': {
                borderColor: '#bbb',
                backgroundColor: 'rgba(0, 0, 0, 0.04)'
              }
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            variant='contained'
            sx={{
              textTransform: 'none',
              backgroundColor: '#f44336',
              color: '#fff',
              '&:hover': {
                backgroundColor: '#d32f2f'
              }
            }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}, arePropsEqual)

// Create a class component wrapper to strictly control rerenders
class DynamicCardWrapper extends Component {
  // Add a custom shouldComponentUpdate to strictly control rerenders
  shouldComponentUpdate(nextProps) {
    // Only rerender if these specific props change
    const criticalProps = ['customUrl', 'currentPage', 'mainGrid']

    // Check if any critical prop has changed
    for (const prop of criticalProps) {
      if (this.props[prop] !== nextProps[prop]) {
        console.log(`Rerendering because ${prop} changed`)

        return true
      }
    }

    // Special check for mainGrid since it's an object
    if (this.props.mainGrid !== nextProps.mainGrid) {
      // If either is undefined and the other isn't, they're different
      if (!this.props.mainGrid || !nextProps.mainGrid) {
        console.log('Rerendering because mainGrid changed (one is undefined)')

        return true
      }

      // Compare individual properties
      if (
        this.props.mainGrid.sm !== nextProps.mainGrid.sm ||
        this.props.mainGrid.md !== nextProps.mainGrid.md ||
        this.props.mainGrid.lg !== nextProps.mainGrid.lg ||
        this.props.mainGrid.xs !== nextProps.mainGrid.xs
      ) {
        console.log('Rerendering because mainGrid properties changed')

        return true
      }
    }

    // Don't rerender for other prop changes
    return false
  }

  render() {
    return <DynamicCard {...this.props} />
  }
}

// Export the wrapped component
export default DynamicCardWrapper
